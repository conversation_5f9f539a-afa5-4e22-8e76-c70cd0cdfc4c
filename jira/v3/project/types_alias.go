package project

import jiraTypes "github.com/ducminhgd/go-atlassian/jira/v3/types"

// Type aliases to preserve backwards compatibility within the project package
// while the canonical definitions live under jira/v3/types.
type (
	Project              = jiraTypes.Project
	User                 = jiraTypes.User
	ProjectComponent     = jiraTypes.ProjectComponent
	IssueType            = jiraTypes.IssueType
	ProjectInsight       = jiraTypes.ProjectInsight
	ProjectListResponse  = jiraTypes.ProjectListResponse
)

